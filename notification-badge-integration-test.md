# Notification Badge Integration Test

## Overview
This document provides testing instructions to verify that push notifications properly increment the PWA badge count.

## What Was Fixed

### Issue
- Test notifications and Formidable Forms notifications were not incrementing the PWA badge
- Notifications were received and displayed but badge count remained unchanged
- No communication between notification system and badge system

### Solution Implemented
1. **Service Worker Badge Integration**: Added badge increment functionality directly in the service worker when notifications are shown
2. **Message Passing**: Implemented communication between service worker and main thread for badge updates
3. **Automatic Badge Sync**: Service worker now automatically increments badge when any notification is received
4. **Cross-Platform Support**: Works for both test notifications and Formidable Forms notifications

## Testing Instructions

### Prerequisites
1. Desktop PWA installed and running (not browser tab)
2. Push notifications enabled
3. Valid Firebase configuration
4. Badge functionality working (test with `[pwa_badge_test]` shortcode first)

### Test 1: Test Notification Badge Increment

1. **Open Desktop PWA** (important: not browser tab)
2. **Clear existing badge**:
   - Open browser console (F12)
   - Run: `window.qPwaBadgeAPI.clearBadge()`
   - Verify badge is cleared from PWA icon

3. **Send Test Notification**:
   - Go to WordPress Admin → Users
   - Find a user with push token
   - Click "Send Test" button
   - **Expected Result**: Badge should increment to 1 on PWA icon

4. **Send Multiple Test Notifications**:
   - Send 2-3 more test notifications
   - **Expected Result**: Badge should increment each time (2, 3, 4, etc.)

### Test 2: Formidable Forms Notification Badge Increment

1. **Clear badge**: `window.qPwaBadgeAPI.clearBadge()`

2. **Submit form with notification action**:
   - Go to a form that has Q-Notify action configured
   - Submit the form
   - **Expected Result**: Badge should increment to 1

3. **Submit multiple forms**:
   - Submit 2-3 more forms
   - **Expected Result**: Badge should increment each time

### Test 3: Badge Persistence and Sync

1. **Set badge to known value**: `window.qPwaBadgeAPI.setBadge(5)`
2. **Send notification**: Use test notification or form submission
3. **Expected Result**: Badge should increment to 6
4. **Close and reopen PWA**
5. **Expected Result**: Badge should still show 6

### Test 4: Console Logging Verification

1. **Open browser console in desktop PWA**
2. **Send a test notification**
3. **Look for these log messages**:
   ```
   Service Worker: Badge incremented to 1
   PWA Badge: Badge incremented by service worker to 1
   PWA Badge: Notification received from service worker
   ```

### Test 5: Service Worker Communication Test

1. **In console, run**:
   ```javascript
   // Listen for service worker messages
   navigator.serviceWorker.addEventListener('message', (event) => {
     console.log('Received SW message:', event.data);
   });
   
   // Send test notification
   // Then check console for BADGE_INCREMENTED and NOTIFICATION_RECEIVED messages
   ```

## Expected Behavior

### ✅ Success Indicators
- Badge increments automatically when notifications are received
- Badge count persists across PWA sessions
- Console shows successful service worker communication
- Both test notifications and form notifications increment badge
- Badge displays correctly on desktop PWA icon

### ❌ Failure Indicators
- Badge doesn't increment when notifications are sent
- Console shows errors about service worker communication
- Badge resets to 0 when PWA is reopened
- No log messages about badge increments

## Troubleshooting

### Badge Not Incrementing
1. **Check PWA Mode**: Ensure running in desktop PWA, not browser tab
2. **Verify Service Worker**: Check if service worker is active
3. **Check Console**: Look for error messages
4. **Test Badge API**: Verify `window.qPwaBadgeAPI.setBadge(1)` works

### Service Worker Issues
1. **Reload Service Worker**: 
   ```javascript
   navigator.serviceWorker.getRegistrations().then(registrations => {
     registrations.forEach(registration => registration.unregister());
   });
   // Then reload PWA
   ```

2. **Check Service Worker Status**:
   ```javascript
   navigator.serviceWorker.ready.then(registration => {
     console.log('SW active:', !!registration.active);
   });
   ```

### Debug Commands

```javascript
// Check current badge count
console.log('Badge count:', window.qPwaBadgeAPI.getBadgeCount());

// Check if in PWA mode
console.log('PWA mode:', window.qPwaBadgeAPI.isPWAMode());

// Check badge API support
console.log('Badge supported:', window.qPwaBadgeAPI.isBadgeSupported());

// Manually increment badge
window.qPwaBadgeAPI.incrementBadge();

// Check localStorage
console.log('Stored count:', localStorage.getItem('q_notification_badge_count'));
```

## Technical Details

### How It Works
1. **Notification Received**: Firebase sends push notification to service worker
2. **Service Worker Processing**: Service worker shows notification and calls `incrementBadgeInServiceWorker()`
3. **Badge Update**: Service worker uses Badging API to set badge on PWA icon
4. **Client Notification**: Service worker sends `BADGE_INCREMENTED` message to main thread
5. **State Sync**: Main thread updates localStorage and internal badge count
6. **Event Dispatch**: Custom events are dispatched for other app components

### Message Flow
```
Notification → Service Worker → Badge API + Client Message → Main Thread → localStorage
```

### Files Modified
- `firebase-messaging-sw.js`: Added badge increment on notification received
- `includes/js/pwa-badge.js`: Added service worker message handling
- Integration ensures all notification types increment badge automatically

## Success Criteria

The integration is working correctly if:
1. ✅ Test notifications increment badge automatically
2. ✅ Formidable Forms notifications increment badge automatically  
3. ✅ Badge count persists across PWA sessions
4. ✅ Console shows proper service worker communication
5. ✅ Badge displays correctly on desktop PWA icon
6. ✅ No errors in browser console related to badge functionality
