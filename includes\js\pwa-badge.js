/**
 * PWA Badge Handler
 *
 * Handles badge API for notification counts on the app icon
 */

(function () {
  // Initialize badge count from localStorage or set to 0
  let badgeCount = parseInt(
    localStorage.getItem("q_notification_badge_count") || "0"
  );

  // Initialize when DOM is ready
  document.addEventListener("DOMContentLoaded", initBadge);

  // Firebase messaging promise
  let messagingPromise;

  // Initialize Firebase messaging if available
  async function getFirebaseMessaging() {
    if (typeof messagingPromise === "undefined") {
      try {
        // Dynamically import Firebase messaging
        const { messaging } = await import(
          "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js"
        );
        messagingPromise = messaging;
        return messaging;
      } catch (error) {
        console.error("Failed to load Firebase messaging:", error);
        return null;
      }
    }
    return messagingPromise;
  }

  /**
   * Check if running in PWA mode
   */
  function isPWAMode() {
    // Check for standalone display mode (desktop and mobile PWA)
    if (window.matchMedia("(display-mode: standalone)").matches) {
      return true;
    }

    // Check for iOS standalone mode
    if (window.navigator.standalone === true) {
      return true;
    }

    // Check for desktop PWA specific indicators
    if (window.matchMedia("(display-mode: window-controls-overlay)").matches) {
      return true;
    }

    return false;
  }

  /**
   * Check if badge API is supported
   */
  function isBadgeSupported() {
    return "setAppBadge" in navigator && "clearAppBadge" in navigator;
  }

  /**
   * Initialize badge functionality
   */
  function initBadge() {
    // Check if badge API is enabled in settings
    if (!window.qPwaBadge || !qPwaBadge.enabled) {
      console.log("PWA Badge: Badge functionality disabled in settings");
      return;
    }

    // Check if we're in PWA mode
    if (!isPWAMode()) {
      console.log(
        "PWA Badge: Not running in PWA mode, badge functionality disabled"
      );
      return;
    }

    // Check if badge API is supported
    if (!isBadgeSupported()) {
      console.log(
        "PWA Badge: Badge API not supported in this browser/environment"
      );
      return;
    }

    console.log("PWA Badge: Initializing badge functionality");

    // Set initial badge count from localStorage or settings
    badgeCount =
      parseInt(localStorage.getItem("q_notification_badge_count")) ||
      qPwaBadge.initialCount ||
      0;

    // Update badge on load
    updateBadge(badgeCount);

    // Listen for notification events
    listenForNotifications();

    // Initialize Firebase messaging if available
    if (typeof messagingPromise !== "undefined") {
      initFirebaseMessaging();
    }

    // Expose badge API
    window.qPwaBadgeAPI = {
      setBadge,
      clearBadge,
      incrementBadge,
      decrementBadge,
      getBadgeCount: () => badgeCount,
      isPWAMode,
      isBadgeSupported,
    };
  }

  /**
   * Initialize Firebase Messaging for badge updates
   */
  async function initFirebaseMessaging() {
    try {
      const messaging = await getFirebaseMessaging();
      if (!messaging) return;

      // Listen for foreground messages to update badge
      const { onMessage } = await import(
        "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging.js"
      );
      onMessage(messaging, (payload) => {
        if (window.location.hostname === "localhost") {
          console.log("Badge handler received message:", payload);
        }
        incrementBadge();
      });
    } catch (error) {
      console.error(
        "Failed to initialize Firebase messaging for badge:",
        error
      );
    }
  }

  /**
   * Update badge count on the app icon
   */
  function updateBadge(count) {
    badgeCount = count;

    // Store the count in localStorage for persistence
    localStorage.setItem("q_notification_badge_count", badgeCount.toString());

    console.log(`PWA Badge: Updating badge count to ${count}`);

    // Update badge via service worker (primary method for desktop PWA)
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready
        .then((registration) => {
          if (registration.active) {
            console.log(
              "PWA Badge: Sending SET_BADGE message to service worker"
            );
            registration.active.postMessage({
              type: "SET_BADGE",
              count: count,
            });
          }
        })
        .catch((error) => {
          console.error(
            "PWA Badge: Error communicating with service worker:",
            error
          );
        });
    }

    // Update badge via Badging API directly (fallback and additional support)
    if ("setAppBadge" in navigator) {
      if (count > 0) {
        navigator
          .setAppBadge(count)
          .then(() => {
            console.log(`PWA Badge: Successfully set badge to ${count}`);
          })
          .catch((error) => {
            console.error("PWA Badge: Error setting app badge:", error);
          });
      } else {
        navigator
          .clearAppBadge()
          .then(() => {
            console.log("PWA Badge: Successfully cleared badge");
          })
          .catch((error) => {
            console.error("PWA Badge: Error clearing app badge:", error);
          });
      }
    } else {
      console.log(
        "PWA Badge: Badging API not available, relying on service worker"
      );
    }

    // Dispatch event for other parts of the app
    document.dispatchEvent(
      new CustomEvent("q_badge_updated", {
        detail: { count: badgeCount },
      })
    );

    // Save badge count to server
    saveBadgeCount(count);

    return badgeCount;
  }

  /**
   * Set badge count
   */
  function setBadge(count) {
    updateBadge(parseInt(count) || 0);
  }

  /**
   * Clear badge count
   */
  function clearBadge() {
    updateBadge(0);
  }

  /**
   * Increment badge count
   */
  function incrementBadge(amount = 1) {
    updateBadge(badgeCount + amount);
  }

  /**
   * Decrement badge count
   */
  function decrementBadge(amount = 1) {
    const newCount = Math.max(0, badgeCount - amount);
    updateBadge(newCount);
  }

  /**
   * Save badge count to server
   */
  function saveBadgeCount(count) {
    if (!qPwaBadge.ajaxurl) return;

    fetch(qPwaBadge.ajaxurl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        action: "q_pwa_set_badge",
        security: qPwaBadge.security || "",
        count: count,
      }),
    })
      .then((response) => response.json())
      .catch((error) => {
        console.error("Failed to save badge count:", error);
      });
  }

  /**
   * Listen for notification events
   */
  function listenForNotifications() {
    // Listen for new notifications
    document.addEventListener("q-notification-received", (event) => {
      incrementBadge();
    });

    // Listen for notification clicks
    document.addEventListener("q-notification-clicked", (event) => {
      decrementBadge();
    });

    // Listen for service worker messages
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.addEventListener("message", (event) => {
        if (event.data && event.data.type === "NOTIFICATION_CLICKED") {
          decrementBadge();
        } else if (event.data && event.data.type === "BADGE_UPDATED") {
          // Sync badge count sent from service worker
          const count = parseInt(event.data.count || 0);
          setBadge(count);
        } else if (event.data && event.data.type === "BADGE_INCREMENTED") {
          // Handle badge increment from service worker
          badgeCount = event.data.count;
          localStorage.setItem(
            "q_notification_badge_count",
            badgeCount.toString()
          );
          console.log(
            `PWA Badge: Badge incremented by service worker to ${badgeCount}`
          );
          // Dispatch event for other parts of the app
          document.dispatchEvent(
            new CustomEvent("q_badge_updated", {
              detail: { count: badgeCount },
            })
          );
        } else if (event.data && event.data.type === "NOTIFICATION_RECEIVED") {
          // Handle notification received from service worker
          console.log(
            "PWA Badge: Notification received from service worker",
            event.data.data
          );
          // Dispatch custom event that other parts of the app can listen to
          document.dispatchEvent(
            new CustomEvent("q-notification-received", {
              detail: event.data.data,
            })
          );
        } else if (event.data && event.data.type === "GET_BADGE_COUNT") {
          // Service worker is requesting current badge count
          sendBadgeCountToServiceWorker();
        }
      });

      // Send current badge count to service worker on initialization
      navigator.serviceWorker.ready.then(() => {
        sendBadgeCountToServiceWorker();
      });
    }

    // Clear badge when user interacts with the page
    document.addEventListener("visibilitychange", () => {
      if (document.visibilityState === "visible") {
        clearBadge();
      }
    });
  }

  /**
   * Send current badge count to service worker
   */
  function sendBadgeCountToServiceWorker() {
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.ready.then((registration) => {
        if (registration.active) {
          registration.active.postMessage({
            type: "CURRENT_BADGE_COUNT",
            count: badgeCount,
          });
        }
      });
    }
  }
})();
