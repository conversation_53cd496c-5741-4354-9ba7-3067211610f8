/**
 * PWA Admin Styles - Tabbed interface and enhanced UI for PWA settings
 */

/* Main Admin Container */
.q-pwa-admin {
  max-width: 1200px;
  padding: 20px;
  margin: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Status Dashboard */
.q-pwa-status-dashboard {
  margin-bottom: 30px;
}

.q-pwa-status-card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.2s ease;
}

.q-pwa-status-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-header h2 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2271b1;
  font-size: 20px;
  font-weight: 600;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid transparent;
}

.status-badge.ready {
  background: #c6efce;
  color: #3e8e41;
  border-color: #8bc34a;
}

.status-badge.incomplete {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.q-pwa-progress {
  margin: 20px 0;
}

.progress-bar {
  width: 100%;
  height: 24px;
  background: #f1f1f1;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 8px;
  position: relative;
  border: 1px solid #e1e1e1;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a32a, #4caf50);
  transition: width 0.5s ease;
  border-radius: 12px;
}

.progress-text {
  margin: 0;
  font-weight: 600;
  color: #1d2327;
  font-size: 14px;
}

/* Requirements Checklist */
.q-pwa-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.q-pwa-requirements li {
  padding: 12px 0;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 14px;
}

.q-pwa-requirements li:last-child {
  border-bottom: none;
}

.q-pwa-requirements li.completed {
  color: #00a32a;
}

.q-pwa-requirements li.pending {
  color: #d63638;
}

.q-pwa-requirements .dashicons {
  margin-top: 2px;
  flex-shrink: 0;
  font-size: 16px;
}

.q-pwa-requirements small {
  display: block;
  color: #666;
  font-style: italic;
  margin-top: 4px;
  font-size: 12px;
}

/* Tabbed Interface */
.q-pwa-tabs-container {
  display: grid;
  grid-template-columns: 200px 1fr;
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.q-pwa-tabs-nav {
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  width: 200px;
  background: #f6f7f7;
}

.q-pwa-tab-button {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #50575e;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f1;
  position: relative;
  white-space: nowrap;
  text-decoration: none;
}

.q-pwa-tab-button:hover {
  background: #fff;
  color: #1d2327;
  text-decoration: none;
}

.q-pwa-tab-button.active {
  background: #fff;
  color: #2271b1;
  border-bottom-color: #2271b1;
  font-weight: 600;
}

.q-pwa-tab-button .dashicons {
  font-size: 16px;
  display: flex;
  align-items: center;
  width: 16px;
  height: 16px;
}

/* Tab Content */
.q-pwa-tab-content {
  display: none;
  padding: 30px;
  grid-column: 2;
  background: #fff;
}

.q-pwa-tab-content.active {
  display: block;
}

.q-pwa-tab-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f1f1;
}

.q-pwa-tab-header h2 {
  margin: 0 0 8px 0;
  color: #1d2327;
  font-size: 23px;
  font-weight: 600;
}

.q-pwa-tab-header p {
  margin: 0;
  color: #646970;
  font-size: 14px;
  line-height: 1.5;
}

/* Form Styling */
.q-pwa-tab-content .form-table {
  margin-top: 0;
  border-collapse: collapse;
  width: 100%;
}

.q-pwa-tab-content .form-table th {
  width: 200px;
  padding: 20px 10px 20px 0;
  font-weight: 600;
  color: #1d2327;
  vertical-align: top;
  text-align: left;
}

.q-pwa-tab-content .form-table td {
  padding: 20px 10px;
  vertical-align: top;
}

.q-pwa-tab-content .form-table tr {
  border-bottom: 1px solid #f1f1f1;
}

.q-pwa-tab-content .form-table tr:last-child {
  border-bottom: none;
}

.q-pwa-tab-content input[type="text"],
.q-pwa-tab-content input[type="url"],
.q-pwa-tab-content input[type="email"],
.q-pwa-tab-content textarea,
.q-pwa-tab-content select {
  width: 100%;
  max-width: 400px;
  padding: 8px 12px;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.q-pwa-tab-content input[type="text"]:focus,
.q-pwa-tab-content input[type="url"]:focus,
.q-pwa-tab-content input[type="email"]:focus,
.q-pwa-tab-content textarea:focus,
.q-pwa-tab-content select:focus {
  border-color: #2271b1;
  outline: none;
  box-shadow: 0 0 0 1px #2271b1;
}

/* Shortcuts Styling */
.q-pwa-shortcut-group {
  background: #f9f9f9;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
  transition: border-color 0.2s ease;
}

.q-pwa-shortcut-group:hover {
  border-color: #ccd0d4;
}

.q-pwa-shortcut-group h5 {
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e5e5;
  font-size: 16px;
  color: #2271b1;
  font-weight: 600;
}

/* Actions */
.q-pwa-actions {
  margin: 30px 0 0 0;
  padding: 20px 30px;
  background: #f6f7f7;
  border-top: 1px solid #ccd0d4;
  text-align: right;
}

.q-pwa-actions .button {
  margin-right: 10px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.q-pwa-actions .button-primary {
  background: #2271b1;
  border-color: #2271b1;
  color: #fff;
}

.q-pwa-actions .button-primary:hover {
  background: #135e96;
  border-color: #135e96;
}

/* Testing Tools */
.q-pwa-testing-tools {
  margin-bottom: 30px;
}

.q-pwa-testing-tools h3 {
  margin-top: 0;
  color: #1d2327;
  font-size: 18px;
  font-weight: 600;
}

.q-pwa-tools {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 20px 0;
}

.q-pwa-tools .button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

.q-pwa-tools .button .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Installation Guide */
.q-pwa-installation-guide {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #f1f1f1;
}

.q-install-instructions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-top: 20px;
}

.instruction-group {
  background: #f9f9f9;
  border: 1px solid #e1e1e1;
  border-radius: 6px;
  padding: 20px;
  transition: border-color 0.2s ease;
}

.instruction-group:hover {
  border-color: #ccd0d4;
}

.instruction-group h4 {
  margin: 0 0 15px 0;
  color: #1d2327;
  font-size: 16px;
  font-weight: 600;
}

.instruction-group ol {
  margin: 0;
  padding-left: 20px;
}

.instruction-group li {
  margin-bottom: 8px;
  color: #50575e;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .q-pwa-tabs-nav {
    flex-wrap: wrap;
  }

  .q-pwa-tab-button {
    padding: 12px 16px;
    flex: 1;
    min-width: 150px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .q-pwa-tabs-nav {
    flex-direction: column;
    position: relative;
  }

  .q-pwa-tab-button {
    width: 100%;
    border-bottom: none;
    justify-content: flex-start;
    padding: 12px 16px;
  }

  .q-pwa-tab-button.active {
    border-bottom: none;
    background: #f0f0f1;
  }

  .q-pwa-tab-button .dashicons {
    width: 20px;
    text-align: center;
  }

  .q-pwa-tab-content {
    padding: 20px;
  }

  .q-install-instructions {
    grid-template-columns: 1fr;
  }

  .status-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .q-pwa-tab-button {
    padding: 12px 16px;
    font-size: 13px;
  }

  .q-pwa-tab-content {
    padding: 15px;
  }

  .q-pwa-tab-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
  }

  .q-pwa-tab-header h2 {
    font-size: 20px;
  }
}

/**
 * PWA App Layout - Application-style layout for PWA settings
 */

/* Main App Container */
.q-pwa-app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f8f9fa;
  margin: 20px 20px 20px 0px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
.q-pwa-header {
  background: #1d2327;
  color: #fff;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.q-pwa-header h1 {
  margin: 0;
  font-size: 20px;
  color: #fff;
  font-weight: 600;
}

/* Main Content Area */
.q-pwa-content {
  display: flex;
  flex: 1;
  background: #fff;
}

/* Left Sidebar */
.q-pwa-sidebar {
  width: 220px;
  background: #f6f7f7;
  border-right: 1px solid #e2e4e7;
}

.q-pwa-tabs-nav {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Main Content */
.q-pwa-main {
  flex: 1;
  max-width: calc(100% - 520px);
  background: #fff;
}

.q-pwa-tab-header {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f1f1f1;
}

.q-pwa-tab-header p {
  margin: 0;
  color: #646970;
  line-height: 1.5;
}

/* Right Sidebar - Overview */
.q-pwa-overview {
  width: 300px;
  background: #fff;
  border-left: 1px solid #e2e4e7;
  padding: 20px;
}

.q-pwa-overview-section {
  margin-bottom: 20px;
}

.q-pwa-overview-section h3 {
  margin-top: 0;
  font-size: 16px;
  border-bottom: 1px solid #f0f0f1;
  padding-bottom: 10px;
  font-weight: 600;
  color: #1d2327;
}

.q-pwa-status-summary {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
  border: 1px solid #e1e1e1;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 10px;
  border: 1px solid transparent;
}

.status-badge.ready {
  background: #c6efce;
  color: #3e8e41;
  border-color: #8bc34a;
}

.status-badge.incomplete {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.q-pwa-quick-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.q-pwa-quick-actions .button {
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s ease;
}

/* Footer */
.q-pwa-footer {
  background: #fff;
  border-top: 1px solid #e2e4e7;
  padding: 15px 20px;
  text-align: center;
  color: #646970;
  font-size: 12px;
}

/* Form Styling */
.q-pwa-actions {
  background: #fff;
  border-top: 1px solid #f0f0f1;
  margin: 0;
  padding: 15px 20px;
  text-align: right;
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 10px;
  background: #f1f1f1;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 8px;
  border: 1px solid #e1e1e1;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00a32a, #4caf50);
  transition: width 0.5s ease;
}

/* Requirements Checklist */
.q-pwa-requirements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.q-pwa-requirements li {
  padding: 8px 0;
  border-bottom: 1px solid #f1f1f1;
  display: flex;
  align-items: flex-start;
  gap: 10px;
  font-size: 14px;
}

.q-pwa-requirements li.completed {
  color: #00a32a;
}

.q-pwa-requirements li.pending {
  color: #d63638;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .q-pwa-content {
    flex-direction: column;
  }

  .q-pwa-sidebar,
  .q-pwa-main,
  .q-pwa-overview {
    width: 100%;
    max-width: 100%;
  }

  .q-pwa-sidebar {
    border-right: none;
    border-bottom: 1px solid #e2e4e7;
  }

  .q-pwa-tabs-nav {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .q-pwa-overview {
    border-left: none;
    border-top: 1px solid #e2e4e7;
  }
}

/**
 * PWA Analytics Dashboard Styles
 */

.q-pwa-analytics-dashboard {
  margin: 20px 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.q-pwa-analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.q-pwa-analytics-period-selector select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 14px;
  background: #fff;
  transition: border-color 0.2s ease;
}

.q-pwa-analytics-period-selector select:focus {
  border-color: #2271b1;
  outline: none;
  box-shadow: 0 0 0 1px #2271b1;
}

.q-pwa-analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.q-pwa-analytics-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e1e1e1;
}

.q-pwa-analytics-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.q-pwa-analytics-card-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #1d2327;
  font-weight: 600;
}

.q-pwa-analytics-card-value {
  font-size: 28px;
  font-weight: bold;
  color: #2271b1;
  margin-bottom: 5px;
}

.q-pwa-analytics-card-trend {
  font-size: 12px;
  margin-top: 5px;
  font-weight: 500;
}

.q-pwa-analytics-card-trend.positive {
  color: #46b450;
}

.q-pwa-analytics-card-trend.negative {
  color: #dc3232;
}

.q-pwa-analytics-chart-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid #e1e1e1;
}

.q-pwa-analytics-chart {
  height: 300px;
  margin-top: 20px;
}

.q-pwa-chart-placeholder {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 250px;
  padding-top: 20px;
  position: relative;
}

.q-pwa-chart-placeholder::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #ddd;
}

.q-pwa-chart-bar {
  width: 30px;
  background: #2271b1;
  border-radius: 3px 3px 0 0;
  position: relative;
  transition: height 0.3s ease;
}

.q-pwa-chart-bar:hover {
  background: #135e96;
}

.q-pwa-chart-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #50575e;
  white-space: nowrap;
  font-weight: 500;
}

.q-pwa-analytics-table-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid #e1e1e1;
}

.q-pwa-analytics-table-container table {
  width: 100%;
  border-collapse: collapse;
}

.q-pwa-analytics-table-container th {
  text-align: left;
  padding: 12px;
  border-bottom: 2px solid #ddd;
  font-weight: 600;
  color: #1d2327;
  background: #f9f9f9;
}

.q-pwa-analytics-table-container td {
  padding: 12px;
  border-bottom: 1px solid #eee;
  color: #50575e;
}

.q-pwa-analytics-table-container tr:hover {
  background: #f9f9f9;
}

.q-pwa-analytics-export {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.q-pwa-analytics-export button {
  padding: 8px 16px;
  background: #f7f7f7;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.q-pwa-analytics-export button:hover {
  background: #f0f0f0;
  border-color: #999;
}

/* Tooltip styles */
#q-pwa-chart-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  pointer-events: none;
  display: none;
  font-weight: 500;
}

#q-pwa-chart-tooltip:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  margin-left: -5px;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid rgba(0, 0, 0, 0.8);
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
  .q-pwa-analytics-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .q-pwa-analytics-period-selector {
    margin-top: 10px;
  }

  .q-pwa-analytics-summary {
    grid-template-columns: 1fr;
  }

  .q-pwa-chart-placeholder {
    overflow-x: auto;
    padding-bottom: 30px;
  }

  .q-pwa-analytics-export {
    justify-content: center;
  }
}

.q-pwa-analytics-dashboard {
  margin: 20px 0;
}

.q-pwa-analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.q-pwa-analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.q-pwa-analytics-card {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 15px;
  text-align: center;
}

.q-pwa-analytics-card-header h3 {
  margin: 0 0 10px 0;
  font-size: 16px;
  color: #555;
}

.q-pwa-analytics-card-value {
  font-size: 28px;
  font-weight: bold;
  color: #0073aa;
}

.q-pwa-analytics-chart-container {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.q-pwa-analytics-chart {
  height: 300px;
  margin-top: 20px;
}

.q-pwa-chart-placeholder {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 250px;
  padding-top: 20px;
}

.q-pwa-chart-bar {
  width: 30px;
  background: #0073aa;
  border-radius: 3px 3px 0 0;
  position: relative;
  transition: height 0.3s ease;
}

.q-pwa-chart-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #555;
}

.q-pwa-analytics-table-container {
  background: #fff;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.q-pwa-analytics-export {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Shortcode Reference Link Styles */
.q-pwa-shortcode-reference-link {
  margin: 30px 0;
}

.q-pwa-shortcode-reference-link .notice {
  padding: 20px;
  border-left: 4px solid #0073aa;
}

.q-pwa-shortcode-reference-link h3 {
  margin-top: 0;
  color: #0073aa;
  font-size: 1.2em;
}

.q-pwa-shortcode-reference-link ul {
  margin-top: 10px;
}

.q-pwa-shortcode-reference-link code {
  background: #f1f3f6;
  color: #d63384;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.9em;
  font-weight: 600;
}

.q-pwa-tab-link {
  text-decoration: none;
  color: #0073aa;
  font-weight: 600;
}

.q-pwa-tab-link:hover {
  color: #005a87;
  text-decoration: underline;
}

/* Splash Screen Validation Styles */
.q-pwa-validation-results {
  margin: 20px 0;
  padding: 15px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.q-pwa-validation-results h3 {
  margin-top: 0;
  color: #333;
  font-size: 16px;
}

.q-pwa-validation-results .notice {
  margin: 10px 0;
}

.q-pwa-validation-results .notice ul {
  margin: 5px 0 0 20px;
}

.q-pwa-validation-results .notice li {
  margin: 3px 0;
  font-size: 14px;
}
