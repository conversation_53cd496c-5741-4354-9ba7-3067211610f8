importScripts(
  "https://www.gstatic.com/firebasejs/11.5.0/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/11.5.0/firebase-messaging-compat.js"
);

console.log("Minimal Service Worker script loaded.");

const Q_PWA_CACHE_NAME = "q-pwa-cache-v1";

// Initialize Firebase with configuration details and error handling
try {
  firebase.initializeApp({
    apiKey: "AIzaSyDQoFn5Xg3JOgTG1BHYiJDJOCyJzX6Zx-Q",
    authDomain: "q-push-50a80.firebaseapp.com",
    projectId: "q-push-50a80",
    storageBucket: "q-push-50a80.appspot.com",
    messagingSenderId: "958555222608",
    appId: "1:958555222608:web:3a9a9a9a9a9a9a9a9a9a9a",
  });
  const messaging = firebase.messaging();

  // Enhanced notification tracking with better validation
  const notificationCache = {
    messages: new Map(),
    cleanupInterval: 300000, // 5 minutes
    maxAge: 300000, // 5 minutes

    addMessage(key, timestamp) {
      if (!key) return false;
      this.cleanup();
      this.messages.set(key, timestamp);
      return true;
    },

    hasMessage(key) {
      if (!key) return false;
      this.cleanup();
      return this.messages.has(key);
    },

    cleanup() {
      const now = Date.now();
      let cleaned = 0;
      for (const [key, timestamp] of this.messages.entries()) {
        if (now - timestamp > this.maxAge) {
          this.messages.delete(key);
          cleaned++;
        }
      }
      if (self.location.hostname === "localhost") {
        console.log(`Cleaned ${cleaned} old notifications`);
      }
    },
  };

  // Handle background messages with improved error handling and prevent duplicate notifications
  messaging.onBackgroundMessage(async (payload) => {
    try {
      if (self.location.hostname === "localhost") {
        console.log("Processing background message:", payload);
      }

      // Handle data-only messages
      const { data, notification } = payload;

      // Use notification object if available, otherwise use data
      const title = notification?.title || data?.title || "New Notification";
      const body = notification?.body || data?.body || "";

      // Generate a unique notification key using multiple properties
      const notificationKey = [
        data?.message_id,
        data?.notification_id,
        title,
        data?.timestamp || Date.now(),
        data?.form_id,
      ]
        .filter(Boolean)
        .join("-");

      if (notificationCache.hasMessage(notificationKey)) {
        if (self.location.hostname === "localhost") {
          console.log("Duplicate notification filtered:", notificationKey);
        }
        return;
      }

      const timestamp = parseInt(data?.timestamp || Date.now() / 1000) * 1000;
      const now = Date.now();

      // Increase max age to 5 minutes to prevent discarding valid notifications
      if (now - timestamp > 5 * 60 * 1000) {
        if (self.location.hostname === "localhost") {
          console.log("Discarding outdated notification:", notificationKey);
        }
        return;
      }

      notificationCache.addMessage(notificationKey, now);

      // Create and show the notification
      const notificationOptions = {
        title: title,
        body: body,
        data: {
          url: data?.click_action || self.registration.scope,
          notification_id: data?.notification_id || "",
          form_id: data?.form_id || "",
          message_id: data?.message_id || notificationKey,
          // Include all data properties for access in notification handlers
          ...data,
        },
        requireInteraction: true,
        tag: notificationKey, // Use the same key for the tag to prevent duplicates
        click_action: data?.click_action || self.registration.scope,
        silent: false, // Ensure sound is played
      };

      // Only add icon if explicitly provided
      if (data.icon) {
        notificationOptions.icon = data.icon;
        notificationOptions.badge = data.badge || data.icon;
      }

      // Only add image if it exists and is a valid URL
      if (data.image && data.image.startsWith("http")) {
        notificationOptions.image = data.image;
      }

      // Process rich media options
      if (data.style) {
        switch (data.style) {
          case "big-picture":
            // Use large_image if available, otherwise fall back to image
            if (data.large_image) {
              notificationOptions.image = data.large_image;
            }
            break;

          case "carousel":
            // Add carousel images if supported
            if (data.carousel_images) {
              try {
                const carouselImages = JSON.parse(data.carousel_images);
                if (
                  Array.isArray(carouselImages) &&
                  carouselImages.length > 0
                ) {
                  // Store images for client-side rendering
                  notificationOptions.data.carouselImages = carouselImages;
                  // Use first image as preview
                  notificationOptions.image = carouselImages[0];
                }
              } catch (e) {
                console.error("Failed to parse carousel images:", e);
              }
            }
            break;

          case "progress":
            // Add progress information
            if (data.progress_current && data.progress_max) {
              notificationOptions.data.progress = {
                current: parseInt(data.progress_current),
                max: parseInt(data.progress_max),
                indeterminate: data.progress_indeterminate === "true",
              };
            }
            break;

          case "map":
            // Add map location data
            if (data.map_lat && data.map_lng) {
              notificationOptions.data.mapLocation = {
                lat: parseFloat(data.map_lat),
                lng: parseFloat(data.map_lng),
                zoom: parseInt(data.map_zoom || 14),
              };
            }
            break;
        }
      }

      // Add video thumbnail if available
      if (data.video_thumbnail) {
        notificationOptions.image = data.video_thumbnail;
        notificationOptions.data.videoUrl = data.video_url || "";
      }

      // Show the notification - works regardless of online/offline status
      if (self.location.hostname === "localhost") {
        console.log(
          "Attempting to show notification:",
          title,
          notificationOptions
        );
        console.log(
          "Network status: ",
          navigator.onLine ? "online" : "offline"
        );
      }
      await self.registration.showNotification(title, notificationOptions);
      if (self.location.hostname === "localhost") {
        console.log(
          "Notification shown successfully (offline capable):",
          title
        );
      }

      // Increment badge count when notification is shown
      incrementBadgeInServiceWorker();

      // Notify all clients that a notification was received
      self.clients.matchAll().then((clients) => {
        clients.forEach((client) => {
          client.postMessage({
            type: "NOTIFICATION_RECEIVED",
            data: {
              title: title,
              body: body,
              notification_id: data?.notification_id || "",
              form_id: data?.form_id || "",
              message_id: data?.message_id || notificationKey,
            },
          });
        });
      });
    } catch (err) {
      console.error(
        "Service Worker: Error showing notification or in onBackgroundMessage",
        err
      );
      // Notifications should still work offline, so this is likely a different issue
      if (self.location.hostname === "localhost") {
        console.error("Notification error details:", {
          online: navigator.onLine,
          title: title,
          error: err.message,
        });
      }
    }
  });
} catch (e) {
  console.error("Service Worker: Firebase initialization failed", e);
}

self.addEventListener("install", function (event) {
  console.log("Service Worker installing.");
  event.waitUntil(
    (async () => {
      const cache = await caches.open(Q_PWA_CACHE_NAME);
      await cache.addAll([
        Q_PWA_OFFLINE_URL,
        // Elementor CSS (uploads)
        "/wp-content/uploads/elementor/css/post-11.css",
        "/wp-content/uploads/elementor/css/post-47.css",
        "/wp-content/uploads/elementor/css/post-6.css",
        // Elementor core CSS
        "/wp-content/plugins/elementor/assets/css/frontend.min.css",
        // Elementor widget CSS (add more as needed)
        "/wp-content/plugins/elementor/assets/css/widget-heading.min.css",
        "/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css",
        "/wp-content/plugins/elementor/assets/css/widget-image.min.css",
        // Theme CSS
        "/wp-content/themes/hello-elementor/style.css",
        "/wp-content/themes/hello-elementor/assets/css/theme.css",
        "/wp-content/themes/hello-elementor/assets/css/reset.css",
        "/wp-content/themes/hello-elementor/assets/css/header-footer.css",
        // Plugin CSS
        "/wp-content/plugins/q-pusher-q-pwa/includes/css/pwa-styles.css",
        // Elementor JS (core)
        "/wp-content/plugins/elementor/assets/js/frontend.min.js",
        // Add more JS as needed
      ]);
    })()
  );
});

self.addEventListener("activate", (event) => {
  console.log("Service Worker activating.");
  // Perform activation steps here, e.g., cleaning up old caches
  // event.waitUntil(
  //   caches.keys().then((cacheNames) => {
  //     return Promise.all(
  //       cacheNames.map((cacheName) => {
  //         if (cacheName !== 'my-cache-name') {
  //           console.log('Service Worker deleting old cache:', cacheName);
  //           return caches.delete(cacheName);
  //         }
  //       })
  //     );
  //   })
  // );
});

// Badge management in service worker
let badgeCount = 0;

// Load badge count from clients on service worker activation
self.addEventListener("activate", (event) => {
  event.waitUntil(loadBadgeCount());
});

// Load badge count from clients
function loadBadgeCount() {
  // Try to get badge count from clients first
  return self.clients.matchAll().then((clients) => {
    if (clients.length > 0) {
      clients[0].postMessage({
        type: "GET_BADGE_COUNT",
      });
    }
  });
}

// Increment badge count in service worker
function incrementBadgeInServiceWorker() {
  badgeCount++;

  // Set badge using the Badging API if available
  if (self.navigator && self.navigator.setAppBadge) {
    self.navigator
      .setAppBadge(badgeCount)
      .then(() => {
        console.log(`Service Worker: Badge incremented to ${badgeCount}`);
      })
      .catch((error) => {
        console.error("Service Worker: Error setting app badge:", error);
      });
  }

  // Notify all clients about badge increment
  self.clients.matchAll().then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: "BADGE_INCREMENTED",
        count: badgeCount,
      });
    });
  });
}

// Handle messages from main thread
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SET_BADGE") {
    const count = parseInt(event.data.count) || 0;
    badgeCount = count; // Update service worker badge count

    // Set badge using the Badging API if available
    if (self.navigator && self.navigator.setAppBadge) {
      if (count > 0) {
        self.navigator.setAppBadge(count).catch((error) => {
          console.error("Service Worker: Error setting app badge:", error);
        });
      } else {
        self.navigator.clearAppBadge().catch((error) => {
          console.error("Service Worker: Error clearing app badge:", error);
        });
      }
    }

    // Notify all clients about badge update
    self.clients.matchAll().then((clients) => {
      clients.forEach((client) => {
        client.postMessage({
          type: "BADGE_UPDATED",
          count: count,
        });
      });
    });
  } else if (event.data && event.data.type === "CURRENT_BADGE_COUNT") {
    // Update service worker badge count from client
    badgeCount = parseInt(event.data.count) || 0;
  }
});

// Only handle push and notification events, no fetch or sync events
self.addEventListener("push", (event) => {
  if (!event.data) return;
  let data = {};
  try {
    data = event.data.json();
  } catch (err) {
    return;
  }
  if (data && data.type === "pwa-update") {
    event.waitUntil(handlePWAUpdate(data));
  }
});

function handlePWAUpdate(data) {
  if (self.location.hostname === "localhost") {
    console.log("Service Worker: PWA update available", data);
  }
  self.clients.matchAll().then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: "PWA_UPDATE_AVAILABLE",
        data: data,
      });
    });
  });
}

// Add notification click event handler
self.addEventListener("notificationclick", function (event) {
  event.notification.close();
  let url =
    event.notification.data && event.notification.data.url
      ? event.notification.data.url
      : self.registration.scope;
  event.waitUntil(
    self.clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then(function (clientList) {
        // If a window/tab is already open with the target URL, focus it; otherwise, open a new one
        for (let i = 0; i < clientList.length; i++) {
          let client = clientList[i];
          if (client.url === url && "focus" in client) {
            return client.focus();
          }
        }
        if (self.clients.openWindow) {
          return self.clients.openWindow(url);
        }
      })
  );
});

// --- OFFLINE & CACHING STRATEGY CONFIGURATION ---
const Q_PWA_CACHING_STRATEGY = "cache-first";
const Q_PWA_OFFLINE_URL = "https://q-studio.local/no-network-offline/";

self.addEventListener("fetch", function (event) {
  const req = event.request;
  // Serve CSS/JS from cache when offline
  if (req.destination === "style" || req.destination === "script") {
    event.respondWith(
      caches.match(req).then(function (response) {
        return (
          response ||
          fetch(req).then(function (networkResponse) {
            // Optionally cache new requests
            return caches.open(Q_PWA_CACHE_NAME).then(function (cache) {
              cache.put(req, networkResponse.clone());
              return networkResponse;
            });
          })
        );
      })
    );
    return;
  }
  // Only handle navigation requests (HTML pages)
  if (
    req.mode === "navigate" ||
    (req.method === "GET" &&
      req.headers.get("accept") &&
      req.headers.get("accept").includes("text/html"))
  ) {
    event.respondWith(
      (async () => {
        const cache = await caches.open(Q_PWA_CACHE_NAME);
        const cachingStrategy = Q_PWA_CACHING_STRATEGY;
        const offlineUrl = Q_PWA_OFFLINE_URL;
        if (cachingStrategy === "cache-first") {
          // Try cache, then network, then offline
          const cached = await cache.match(req);
          if (cached) return cached;
          try {
            const netRes = await fetch(req);
            if (netRes && netRes.ok) {
              cache.put(req, netRes.clone());
            }
            return netRes;
          } catch (e) {
            if (offlineUrl) {
              const offlineRes = await cache.match(offlineUrl);
              if (offlineRes) return offlineRes;
            }
            throw e;
          }
        } else if (cachingStrategy === "stale-while-revalidate") {
          // Return cache if available, update in background
          const cached = await cache.match(req);
          const fetchPromise = fetch(req)
            .then((netRes) => {
              if (netRes && netRes.ok) {
                cache.put(req, netRes.clone());
              }
              return netRes;
            })
            .catch(() => null);
          if (cached) {
            fetchPromise; // update in background
            return cached;
          }
          const netRes = await fetchPromise;
          if (netRes) return netRes;
          if (offlineUrl) {
            const offlineRes = await cache.match(offlineUrl);
            if (offlineRes) return offlineRes;
          }
          return Response.error();
        } else {
          // network-first (default)
          try {
            const netRes = await fetch(req);
            if (netRes && netRes.ok) {
              cache.put(req, netRes.clone());
            }
            return netRes;
          } catch (e) {
            const cached = await cache.match(req);
            if (cached) return cached;
            if (offlineUrl) {
              const offlineRes = await cache.match(offlineUrl);
              if (offlineRes) return offlineRes;
            }
            throw e;
          }
        }
      })()
    );
  }
});
