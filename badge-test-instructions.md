# PWA Badge Testing Instructions

## Overview
This document provides comprehensive testing instructions for the PWA notification badge functionality, specifically focusing on desktop PWA installations.

## Prerequisites
1. WordPress site with Q-PWA plugin installed and activated
2. P<PERSON> enabled in plugin settings
3. Valid <PERSON>WA manifest and service worker
4. Desktop browser that supports PWA installation (Chrome, Edge, Firefox)

## Testing Environment Setup

### 1. Enable Badge Settings
1. Go to WordPress Admin → Q-PWA Settings
2. Ensure "Enable PWA" is checked
3. Verify "Badge Enabled" setting is enabled (should be enabled by default)
4. Save settings

### 2. Install PWA on Desktop
1. Open your website in a supported browser (Chrome/Edge recommended)
2. Look for the PWA install prompt or use browser menu → "Install [Site Name]"
3. Complete the installation process
4. Launch the PWA from desktop/start menu (not from browser)

## Testing Procedures

### Test 1: Basic Badge API Support Detection
1. Open the desktop PWA
2. Open browser developer tools (F12)
3. Go to Console tab
4. Look for these log messages:
   - "PWA Badge: Initializing badge functionality"
   - Should NOT see "Badge functionality disabled" or "Badge API not supported"

### Test 2: Manual Badge Setting
1. In the PWA console, run:
   ```javascript
   // Test if badge API is available
   console.log('Badge API available:', window.qPwaBadgeAPI);
   
   // Set badge to 5
   window.qPwaBadgeAPI.setBadge(5);
   
   // Check current count
   console.log('Current badge count:', window.qPwaBadgeAPI.getBadgeCount());
   ```
2. Check the PWA icon in taskbar/dock for a badge showing "5"

### Test 3: Badge Increment/Decrement
1. In console, run:
   ```javascript
   // Increment badge
   window.qPwaBadgeAPI.incrementBadge();
   console.log('After increment:', window.qPwaBadgeAPI.getBadgeCount());
   
   // Decrement badge
   window.qPwaBadgeAPI.decrementBadge();
   console.log('After decrement:', window.qPwaBadgeAPI.getBadgeCount());
   ```
2. Verify badge count changes on the PWA icon

### Test 4: Badge Clear
1. In console, run:
   ```javascript
   // Clear badge
   window.qPwaBadgeAPI.clearBadge();
   console.log('After clear:', window.qPwaBadgeAPI.getBadgeCount());
   ```
2. Verify badge disappears from PWA icon

### Test 5: Service Worker Communication
1. In console, run:
   ```javascript
   // Test service worker communication
   navigator.serviceWorker.ready.then(registration => {
     registration.active.postMessage({
       type: 'SET_BADGE',
       count: 3
     });
   });
   ```
2. Check if badge shows "3" on PWA icon

### Test 6: Persistence Test
1. Set badge to a specific number (e.g., 7)
2. Close the PWA completely
3. Reopen the PWA from desktop
4. Check if badge count persists

### Test 7: Firebase Notification Integration (if applicable)
1. Send a test push notification through Firebase
2. Verify badge increments when notification is received
3. Click on notification and verify badge decrements

## Expected Results

### Successful Badge Implementation Should Show:
- ✅ Badge appears on PWA icon in taskbar/dock
- ✅ Badge count updates in real-time
- ✅ Badge persists across PWA sessions
- ✅ Console shows successful badge operations
- ✅ No error messages in console related to badge API

### Common Issues and Solutions:

#### Badge Not Appearing
- **Issue**: Badge API not supported
- **Solution**: Ensure using Chrome/Edge and PWA is properly installed
- **Check**: Console should show "PWA Badge: Badge API not supported" if this is the issue

#### Badge Not Updating
- **Issue**: Service worker communication failure
- **Solution**: Check service worker registration and console for errors
- **Check**: Look for "Error communicating with service worker" messages

#### Badge Not Persisting
- **Issue**: localStorage not working or PWA not properly installed
- **Solution**: Verify PWA installation and check localStorage in DevTools

## Browser-Specific Notes

### Chrome/Chromium
- Best support for badge API
- Badge appears on taskbar icon (Windows) or dock (macOS)

### Microsoft Edge
- Good support for badge API
- Similar behavior to Chrome

### Firefox
- Limited badge API support
- May not show badges on all platforms

## Debugging Commands

```javascript
// Check PWA mode
console.log('Is PWA mode:', window.qPwaBadgeAPI?.isPWAMode());

// Check badge support
console.log('Badge supported:', window.qPwaBadgeAPI?.isBadgeSupported());

// Check current settings
console.log('Badge settings:', window.qPwaBadge);

// Check localStorage
console.log('Stored badge count:', localStorage.getItem('q_notification_badge_count'));
```

## Troubleshooting

If badge functionality is not working:

1. **Verify PWA Installation**: Ensure the app is installed as a PWA, not just bookmarked
2. **Check Browser Support**: Use Chrome or Edge for best compatibility
3. **Review Console Logs**: Look for initialization and error messages
4. **Test Badge API Directly**: Use `navigator.setAppBadge(1)` in console
5. **Verify Settings**: Ensure badge is enabled in WordPress admin
6. **Check Service Worker**: Verify service worker is registered and active

## Platform-Specific Testing

### Windows
- Badge appears as a number overlay on taskbar icon
- Test with both Chrome and Edge PWAs

### macOS
- Badge appears as a red circle with number on dock icon
- Test with Chrome PWA primarily

### Linux
- Badge support varies by desktop environment
- Test with Chrome PWA on GNOME/KDE

## Success Criteria

The badge implementation is successful if:
1. Badge appears on desktop PWA icon
2. Badge count can be set, incremented, decremented, and cleared
3. Badge persists across PWA sessions
4. No console errors related to badge functionality
5. Service worker communication works properly
