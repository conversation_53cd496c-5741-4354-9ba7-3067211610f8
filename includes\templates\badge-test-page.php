<?php

/**
 * PWA Badge Test Page Template
 * 
 * This template provides a user interface for testing PWA badge functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="q-pwa-badge-test-container" style="max-width: 800px; margin: 20px auto; padding: 20px; font-family: Arial, sans-serif;">
    <h1>PWA Badge Test Page</h1>

    <div class="badge-status" style="background: #f0f0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <h3>Badge Status</h3>
        <div id="badge-status-info">
            <p><strong>PWA Mode:</strong> <span id="pwa-mode-status">Checking...</span></p>
            <p><strong>Badge API Support:</strong> <span id="badge-api-status">Checking...</span></p>
            <p><strong>Current Badge Count:</strong> <span id="current-badge-count">0</span></p>
            <p><strong>Badge Enabled in Settings:</strong> <span id="badge-enabled-status">Checking...</span></p>
        </div>
    </div>

    <div class="badge-controls" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px;">
        <h3>Badge Controls</h3>

        <div style="margin-bottom: 15px;">
            <label for="badge-count-input">Set Badge Count:</label>
            <input type="number" id="badge-count-input" min="0" max="99" value="0" style="margin-left: 10px; padding: 5px;">
            <button id="set-badge-btn" style="margin-left: 10px; padding: 5px 15px;">Set Badge</button>
        </div>

        <div style="margin-bottom: 15px;">
            <button id="increment-badge-btn" style="padding: 5px 15px; margin-right: 10px;">Increment (+1)</button>
            <button id="decrement-badge-btn" style="padding: 5px 15px; margin-right: 10px;">Decrement (-1)</button>
            <button id="clear-badge-btn" style="padding: 5px 15px;">Clear Badge</button>
        </div>
    </div>

    <div class="test-scenarios" style="background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 20px;">
        <h3>Test Scenarios</h3>

        <button id="test-basic-functionality" style="display: block; margin-bottom: 10px; padding: 10px 15px; width: 100%;">
            Test 1: Basic Functionality
        </button>

        <button id="test-persistence" style="display: block; margin-bottom: 10px; padding: 10px 15px; width: 100%;">
            Test 2: Persistence (Set badge to 5 for persistence test)
        </button>

        <button id="test-service-worker" style="display: block; margin-bottom: 10px; padding: 10px 15px; width: 100%;">
            Test 3: Service Worker Communication
        </button>

        <button id="simulate-notification" style="display: block; margin-bottom: 10px; padding: 10px 15px; width: 100%;">
            Test 4: Simulate Notification Received
        </button>
    </div>

    <div class="console-output" style="background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto;">
        <h4 style="color: #fff; margin-top: 0;">Console Output</h4>
        <div id="console-output"></div>
    </div>

    <div class="instructions" style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 20px;">
        <h3>Instructions</h3>
        <ol>
            <li><strong>Ensure you're running this in a desktop PWA</strong> (not in a browser tab)</li>
            <li>Check the status information above to verify PWA mode and badge support</li>
            <li>Use the badge controls to test setting, incrementing, and clearing badges</li>
            <li>Look at your PWA icon in the taskbar/dock to see the badge changes</li>
            <li>Run the test scenarios to verify different aspects of badge functionality</li>
            <li>Check the console output for detailed logging information</li>
        </ol>

        <p><strong>Note:</strong> If you see "Not in PWA mode" or "Badge API not supported", make sure you've properly installed the PWA and are running it from the desktop shortcut, not from a browser tab.</p>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const consoleOutput = document.getElementById('console-output');

        // Custom console logging
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#0f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;

            // Also log to browser console
            console.log(`PWA Badge Test: ${message}`);
        }

        // Update status information
        function updateStatus() {
            // Check PWA mode
            const isPWA = window.qPwaBadgeAPI?.isPWAMode() || false;
            document.getElementById('pwa-mode-status').textContent = isPWA ? 'Yes ✅' : 'No ❌';
            document.getElementById('pwa-mode-status').style.color = isPWA ? 'green' : 'red';

            // Check badge API support
            const badgeSupported = window.qPwaBadgeAPI?.isBadgeSupported() || false;
            document.getElementById('badge-api-status').textContent = badgeSupported ? 'Yes ✅' : 'No ❌';
            document.getElementById('badge-api-status').style.color = badgeSupported ? 'green' : 'red';

            // Check badge enabled in settings
            const badgeEnabled = window.qPwaBadge?.enabled || false;
            document.getElementById('badge-enabled-status').textContent = badgeEnabled ? 'Yes ✅' : 'No ❌';
            document.getElementById('badge-enabled-status').style.color = badgeEnabled ? 'green' : 'red';

            // Update current badge count
            const currentCount = window.qPwaBadgeAPI?.getBadgeCount() || 0;
            document.getElementById('current-badge-count').textContent = currentCount;

            logToConsole(`Status updated - PWA: ${isPWA}, Badge API: ${badgeSupported}, Enabled: ${badgeEnabled}, Count: ${currentCount}`);
        }

        // Initialize status
        setTimeout(updateStatus, 1000); // Wait for badge API to initialize

        // Badge control event listeners
        document.getElementById('set-badge-btn').addEventListener('click', function() {
            const count = parseInt(document.getElementById('badge-count-input').value) || 0;
            if (window.qPwaBadgeAPI) {
                window.qPwaBadgeAPI.setBadge(count);
                logToConsole(`Set badge to ${count}`, 'success');
                updateStatus();
            } else {
                logToConsole('Badge API not available', 'error');
            }
        });

        document.getElementById('increment-badge-btn').addEventListener('click', function() {
            if (window.qPwaBadgeAPI) {
                window.qPwaBadgeAPI.incrementBadge();
                logToConsole('Incremented badge', 'success');
                updateStatus();
            } else {
                logToConsole('Badge API not available', 'error');
            }
        });

        document.getElementById('decrement-badge-btn').addEventListener('click', function() {
            if (window.qPwaBadgeAPI) {
                window.qPwaBadgeAPI.decrementBadge();
                logToConsole('Decremented badge', 'success');
                updateStatus();
            } else {
                logToConsole('Badge API not available', 'error');
            }
        });

        document.getElementById('clear-badge-btn').addEventListener('click', function() {
            if (window.qPwaBadgeAPI) {
                window.qPwaBadgeAPI.clearBadge();
                logToConsole('Cleared badge', 'success');
                updateStatus();
            } else {
                logToConsole('Badge API not available', 'error');
            }
        });

        // Test scenario event listeners
        document.getElementById('test-basic-functionality').addEventListener('click', function() {
            logToConsole('Starting basic functionality test...');

            if (!window.qPwaBadgeAPI) {
                logToConsole('Badge API not available - test failed', 'error');
                return;
            }

            // Test sequence
            setTimeout(() => {
                window.qPwaBadgeAPI.setBadge(3);
                logToConsole('Set badge to 3');
            }, 500);

            setTimeout(() => {
                window.qPwaBadgeAPI.incrementBadge();
                logToConsole('Incremented to 4');
            }, 1500);

            setTimeout(() => {
                window.qPwaBadgeAPI.decrementBadge();
                logToConsole('Decremented to 3');
            }, 2500);

            setTimeout(() => {
                window.qPwaBadgeAPI.clearBadge();
                logToConsole('Cleared badge - Basic test complete', 'success');
                updateStatus();
            }, 3500);
        });

        document.getElementById('test-persistence').addEventListener('click', function() {
            if (window.qPwaBadgeAPI) {
                window.qPwaBadgeAPI.setBadge(5);
                logToConsole('Set badge to 5 for persistence test. Close and reopen PWA to verify persistence.', 'success');
                updateStatus();
            } else {
                logToConsole('Badge API not available', 'error');
            }
        });

        document.getElementById('test-service-worker').addEventListener('click', function() {
            logToConsole('Testing service worker communication...');

            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.ready.then(registration => {
                    if (registration.active) {
                        registration.active.postMessage({
                            type: 'SET_BADGE',
                            count: 7
                        });
                        logToConsole('Sent SET_BADGE message to service worker with count 7', 'success');
                        setTimeout(updateStatus, 500);
                    } else {
                        logToConsole('Service worker not active', 'error');
                    }
                }).catch(error => {
                    logToConsole(`Service worker error: ${error.message}`, 'error');
                });
            } else {
                logToConsole('Service worker not supported', 'error');
            }
        });

        document.getElementById('simulate-notification').addEventListener('click', function() {
            logToConsole('Simulating notification received...');

            // Dispatch custom event that badge handler listens for
            document.dispatchEvent(new CustomEvent('q-notification-received', {
                detail: {
                    message: 'Test notification'
                }
            }));

            logToConsole('Dispatched q-notification-received event', 'success');
            setTimeout(updateStatus, 500);
        });

        // Listen for badge updates
        document.addEventListener('q_badge_updated', function(event) {
            logToConsole(`Badge updated event received: count = ${event.detail.count}`, 'success');
            updateStatus();
        });

        logToConsole('PWA Badge Test Page initialized');
    });
</script>